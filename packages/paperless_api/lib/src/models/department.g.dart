// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'department.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$DepartmentsCWProxy {
  Departments count(int count);

  Departments results(List<Department> results);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `Departments(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// Departments(...).copyWith(id: 12, name: "My name")
  /// ````
  Departments call({
    int? count,
    List<Department>? results,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfDepartments.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfDepartments.copyWith.fieldName(...)`
class _$DepartmentsCWProxyImpl implements _$DepartmentsCWProxy {
  const _$DepartmentsCWProxyImpl(this._value);

  final Departments _value;

  @override
  Departments count(int count) => this(count: count);

  @override
  Departments results(List<Department> results) => this(results: results);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `Departments(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// Departments(...).copyWith(id: 12, name: "My name")
  /// ````
  Departments call({
    Object? count = const $CopyWithPlaceholder(),
    Object? results = const $CopyWithPlaceholder(),
  }) {
    return Departments(
      count: count == const $CopyWithPlaceholder() || count == null
          ? _value.count
          // ignore: cast_nullable_to_non_nullable
          : count as int,
      results: results == const $CopyWithPlaceholder() || results == null
          ? _value.results
          // ignore: cast_nullable_to_non_nullable
          : results as List<Department>,
    );
  }
}

extension $DepartmentsCopyWith on Departments {
  /// Returns a callable class that can be used as follows: `instanceOfDepartments.copyWith(...)` or like so:`instanceOfDepartments.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$DepartmentsCWProxy get copyWith => _$DepartmentsCWProxyImpl(this);
}

abstract class _$DepartmentCWProxy {
  Department id(int id);

  Department slug(String? slug);

  Department name(String name);

  Department match(String? match);

  Department matchingAlgorithm(int? matchingAlgorithm);

  Department isInsensitive(bool? isInsensitive);

  Department isInboxDepartment(bool? isInboxDepartment);

  Department documentCount(int? documentCount);

  Department owner(int? owner);

  Department userCanChange(bool? userCanChange);

  Department isSelected(bool isSelected);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `Department(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// Department(...).copyWith(id: 12, name: "My name")
  /// ````
  Department call({
    int? id,
    String? slug,
    String? name,
    String? match,
    int? matchingAlgorithm,
    bool? isInsensitive,
    bool? isInboxDepartment,
    int? documentCount,
    int? owner,
    bool? userCanChange,
    bool? isSelected,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfDepartment.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfDepartment.copyWith.fieldName(...)`
class _$DepartmentCWProxyImpl implements _$DepartmentCWProxy {
  const _$DepartmentCWProxyImpl(this._value);

  final Department _value;

  @override
  Department id(int id) => this(id: id);

  @override
  Department slug(String? slug) => this(slug: slug);

  @override
  Department name(String name) => this(name: name);

  @override
  Department match(String? match) => this(match: match);

  @override
  Department matchingAlgorithm(int? matchingAlgorithm) =>
      this(matchingAlgorithm: matchingAlgorithm);

  @override
  Department isInsensitive(bool? isInsensitive) =>
      this(isInsensitive: isInsensitive);

  @override
  Department isInboxDepartment(bool? isInboxDepartment) =>
      this(isInboxDepartment: isInboxDepartment);

  @override
  Department documentCount(int? documentCount) =>
      this(documentCount: documentCount);

  @override
  Department owner(int? owner) => this(owner: owner);

  @override
  Department userCanChange(bool? userCanChange) =>
      this(userCanChange: userCanChange);

  @override
  Department isSelected(bool isSelected) => this(isSelected: isSelected);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `Department(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// Department(...).copyWith(id: 12, name: "My name")
  /// ````
  Department call({
    Object? id = const $CopyWithPlaceholder(),
    Object? slug = const $CopyWithPlaceholder(),
    Object? name = const $CopyWithPlaceholder(),
    Object? match = const $CopyWithPlaceholder(),
    Object? matchingAlgorithm = const $CopyWithPlaceholder(),
    Object? isInsensitive = const $CopyWithPlaceholder(),
    Object? isInboxDepartment = const $CopyWithPlaceholder(),
    Object? documentCount = const $CopyWithPlaceholder(),
    Object? owner = const $CopyWithPlaceholder(),
    Object? userCanChange = const $CopyWithPlaceholder(),
    Object? isSelected = const $CopyWithPlaceholder(),
  }) {
    return Department(
      id: id == const $CopyWithPlaceholder() || id == null
          ? _value.id
          // ignore: cast_nullable_to_non_nullable
          : id as int,
      slug: slug == const $CopyWithPlaceholder()
          ? _value.slug
          // ignore: cast_nullable_to_non_nullable
          : slug as String?,
      name: name == const $CopyWithPlaceholder() || name == null
          ? _value.name
          // ignore: cast_nullable_to_non_nullable
          : name as String,
      match: match == const $CopyWithPlaceholder()
          ? _value.match
          // ignore: cast_nullable_to_non_nullable
          : match as String?,
      matchingAlgorithm: matchingAlgorithm == const $CopyWithPlaceholder()
          ? _value.matchingAlgorithm
          // ignore: cast_nullable_to_non_nullable
          : matchingAlgorithm as int?,
      isInsensitive: isInsensitive == const $CopyWithPlaceholder()
          ? _value.isInsensitive
          // ignore: cast_nullable_to_non_nullable
          : isInsensitive as bool?,
      isInboxDepartment: isInboxDepartment == const $CopyWithPlaceholder()
          ? _value.isInboxDepartment
          // ignore: cast_nullable_to_non_nullable
          : isInboxDepartment as bool?,
      documentCount: documentCount == const $CopyWithPlaceholder()
          ? _value.documentCount
          // ignore: cast_nullable_to_non_nullable
          : documentCount as int?,
      owner: owner == const $CopyWithPlaceholder()
          ? _value.owner
          // ignore: cast_nullable_to_non_nullable
          : owner as int?,
      userCanChange: userCanChange == const $CopyWithPlaceholder()
          ? _value.userCanChange
          // ignore: cast_nullable_to_non_nullable
          : userCanChange as bool?,
      isSelected:
          isSelected == const $CopyWithPlaceholder() || isSelected == null
              ? _value.isSelected
              // ignore: cast_nullable_to_non_nullable
              : isSelected as bool,
    );
  }
}

extension $DepartmentCopyWith on Department {
  /// Returns a callable class that can be used as follows: `instanceOfDepartment.copyWith(...)` or like so:`instanceOfDepartment.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$DepartmentCWProxy get copyWith => _$DepartmentCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Departments _$DepartmentsFromJson(Map<String, dynamic> json) => Departments(
      count: (json['count'] as num).toInt(),
      results: (json['results'] as List<dynamic>)
          .map((e) => Department.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$DepartmentsToJson(Departments instance) =>
    <String, dynamic>{
      'count': instance.count,
      'results': instance.results,
    };

Department _$DepartmentFromJson(Map<String, dynamic> json) => Department(
      id: (json['id'] as num).toInt(),
      slug: json['slug'] as String?,
      name: json['name'] as String,
      match: json['match'] as String?,
      matchingAlgorithm: (json['matching_algorithm'] as num?)?.toInt(),
      isInsensitive: json['is_insensitive'] as bool?,
      isInboxDepartment: json['is_inbox_department'] as bool?,
      documentCount: (json['document_count'] as num?)?.toInt(),
      owner: (json['owner'] as num?)?.toInt(),
      userCanChange: json['user_can_change'] as bool?,
      isSelected: json['isSelected'] as bool? ?? false,
    );

Map<String, dynamic> _$DepartmentToJson(Department instance) =>
    <String, dynamic>{
      'id': instance.id,
      'slug': instance.slug,
      'name': instance.name,
      'match': instance.match,
      'matching_algorithm': instance.matchingAlgorithm,
      'is_insensitive': instance.isInsensitive,
      'is_inbox_department': instance.isInboxDepartment,
      'document_count': instance.documentCount,
      'owner': instance.owner,
      'user_can_change': instance.userCanChange,
      'isSelected': instance.isSelected,
    };
