import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
part 'department.g.dart';

@JsonSerializable()
@CopyWith()
class Departments {
  int count;

  List<Department> results;

  Departments({
    required this.count,
    required this.results,
  });

  factory Departments.fromJson(Map<String, dynamic> json) =>
      _$DepartmentsFromJson(json);

  Map<String, dynamic> toJson() => _$DepartmentsToJson(this);
}

@JsonSerializable()
@CopyWith()
class Department {
  int id;
  String? slug;
  String name;
  String? match;
  @JsonKey(name: 'matching_algorithm')
  int? matchingAlgorithm;
  @Json<PERSON>ey(name: 'is_insensitive')
  bool? isInsensitive;
  @<PERSON>sonKey(name: 'is_inbox_department')
  bool? isInboxDepartment;
  @<PERSON>sonKey(name: 'document_count')
  int? documentCount;
  int? owner;
  @<PERSON>son<PERSON>ey(name: 'user_can_change')
  bool? userCanChange;
  bool isSelected;

  Department({
    required this.id,
     this.slug,
    required this.name,
     this.match,
     this.matchingAlgorithm,
     this.isInsensitive,
     this.isInboxDepartment,
     this.documentCount,
     this.owner,
     this.userCanChange,
    this.isSelected = false,
  });

  factory Department.fromJson(Map<String, dynamic> json) =>
      _$DepartmentFromJson(json);

  Map<String, dynamic> toJson() => _$DepartmentToJson(this);
}
